package main

func main() {

}

func calculate(s string) int {
	op, num := 1, 0
	nums := make([]int, 0)
	ops := make([]int, 0)
	ans := 0
	for i := 0; i < len(s); i++ {
		c := s[i]
		switch c {
		case ' ':
			continue
		case '+':
			ans += op * num
			op, num = 1, 0
		case '-':
			ans += op * num
			op, num = -1, 0
		case '(':
			ans += op * num
			nums = append(nums, ans)
			ops = append(ops, op)
			op, num, ans = 1, 0, 0
		case ')':
			ans += op * num
			ans *= ops[len(ops)-1]
			ans += nums[len(nums)-1]
			ops = ops[:len(ops)-1]
			nums = nums[:len(nums)-1]
			op, num = 1, 0
		default:
			num = num*10 + int(c) - int('0')
		}
	}
	return ans + op*num
}
